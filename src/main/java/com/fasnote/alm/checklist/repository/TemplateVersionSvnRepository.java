package com.fasnote.alm.checklist.repository;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.fasnote.alm.checklist.model.ReviewTemplateVersion;
import com.fasnote.alm.checklist.model.TemplateSnapshot;
import com.fasnote.alm.checklist.service.IRepositoryFileService;
import com.fasnote.alm.checklist.util.JsonUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.polarion.alm.projects.IProjectService;
import com.polarion.alm.tracker.ITrackerService;
import com.polarion.alm.tracker.model.IWorkItem;
import com.polarion.platform.core.PlatformContext;
import com.polarion.subterra.base.location.ILocation;

/**
 * 模板版本SVN存储实现
 */
@Repository("templateVersionSvnRepository")
public class TemplateVersionSvnRepository implements TemplateVersionRepository {
    
    private static final Logger logger = LoggerFactory.getLogger(TemplateVersionSvnRepository.class);
    private static final String SNAPSHOTS_DIRECTORY = "checklist/template-snapshots";
    private static final String FILE_EXTENSION = ".json";
    private static final String REVIEW_VERSION_FILE = "checklist-template-version.json";

    // 项目位置缓存，避免重复获取
    private final ConcurrentMap<String, ILocation> projectLocationCache = new ConcurrentHashMap<>();

    @Autowired
    private IRepositoryFileService repositoryFileService;

    @Autowired
    private ObjectMapper objectMapper;
    
    @Override
    public TemplateSnapshot saveSnapshot(String projectId, TemplateSnapshot snapshot) throws IOException {
        if (projectId == null || projectId.trim().isEmpty()) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        
        try {
            // 快照存储在项目目录下的checklist/template-snapshots目录，与模板保持一致的目录结构
            // 路径结构：{projectRoot}/checklist/template-snapshots/
            // 对应模板路径：{projectRoot}/checklist/template/
            ILocation snapshotFile = getSnapshotFileLocation(projectId, snapshot.getId());
            
            String jsonContent = objectMapper.writeValueAsString(snapshot);
            boolean success = repositoryFileService.writeFile(snapshotFile, jsonContent);
            if (!success) {
                throw new IOException("保存快照失败: " + snapshot.getId());
            }
            
            return snapshot;
        } catch (Exception e) {
            logger.error("Failed to save snapshot: {} in project: {}", snapshot.getId(), projectId, e);
            throw new IOException("SVN保存快照失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public Optional<TemplateSnapshot> findSnapshotById(String projectId, String snapshotId) throws IOException {
        if (projectId == null || projectId.trim().isEmpty() || 
            snapshotId == null || snapshotId.trim().isEmpty()) {
            return Optional.empty();
        }
        
        try {
            ILocation snapshotFile = getSnapshotFileLocation(projectId, snapshotId);
            if (!repositoryFileService.exists(snapshotFile)) {
                return Optional.empty();
            }
            
            InputStream inputStream = repositoryFileService.readFile(snapshotFile);
            if (inputStream == null) {
                return Optional.empty();
            }
            
            String jsonContent = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
            TemplateSnapshot snapshot = objectMapper.readValue(jsonContent, TemplateSnapshot.class);
            return Optional.of(snapshot);
        } catch (Exception e) {
            logger.error("Failed to load snapshot: {} from project: {}", snapshotId, projectId, e);
            return Optional.empty();
        }
    }
    
    @Override
    public List<TemplateSnapshot> findSnapshotsByTemplateId(String projectId, String templateId, int limit, int offset) throws IOException {
        // SVN存储模式暂不支持复杂查询，返回空列表
        logger.warn("SVN存储模式不支持按模板ID查询快照列表");
        return new ArrayList<>();
    }
    
    @Override
    public boolean deleteSnapshot(String projectId, String snapshotId) throws IOException {
        if (projectId == null || projectId.trim().isEmpty() || 
            snapshotId == null || snapshotId.trim().isEmpty()) {
            return false;
        }
        
        try {
            ILocation snapshotFile = getSnapshotFileLocation(projectId, snapshotId);
            return repositoryFileService.deleteFile(snapshotFile);
        } catch (Exception e) {
            logger.error("Failed to delete snapshot: {} from project: {}", snapshotId, projectId, e);
            return false;
        }
    }
    
    @Override
    public ReviewTemplateVersion saveReviewTemplateVersion(String projectId, ReviewTemplateVersion reviewVersion) throws IOException {
        if (projectId == null || projectId.trim().isEmpty()) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        
        try {
            ILocation workItemLocation = getWorkItemLocation(projectId, reviewVersion.getReviewId());
            if (workItemLocation == null) {
                throw new IOException("无法获取工作项位置");
            }
            
            ILocation versionFile = workItemLocation.append(REVIEW_VERSION_FILE);
            String jsonContent = objectMapper.writeValueAsString(reviewVersion);
            boolean success = repositoryFileService.writeFile(versionFile, jsonContent);
            if (!success) {
                throw new IOException("保存版本关联信息失败");
            }
            
            return reviewVersion;
        } catch (Exception e) {
            logger.error("Failed to save review template version for review: {} in project: {}", 
                reviewVersion.getReviewId(), projectId, e);
            throw new IOException("SVN保存版本关联失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public Optional<ReviewTemplateVersion> findReviewTemplateVersionByReviewId(String projectId, String reviewId) throws IOException {
        if (projectId == null || projectId.trim().isEmpty() || 
            reviewId == null || reviewId.trim().isEmpty()) {
            return Optional.empty();
        }
        
        try {
            ILocation workItemLocation = getWorkItemLocation(projectId, reviewId);
            if (workItemLocation == null) {
                return Optional.empty();
            }
            
            ILocation versionFile = workItemLocation.append(REVIEW_VERSION_FILE);
            if (!repositoryFileService.exists(versionFile)) {
                return Optional.empty();
            }
            
            InputStream inputStream = repositoryFileService.readFile(versionFile);
            if (inputStream == null) {
                return Optional.empty();
            }
            
            String jsonContent = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
            ReviewTemplateVersion reviewVersion = JsonUtil.fromJson(jsonContent, ReviewTemplateVersion.class);
            return Optional.of(reviewVersion);
        } catch (Exception e) {
            logger.error("Failed to load review template version for review: {} from project: {}", reviewId, projectId, e);
            return Optional.empty();
        }
    }
    
    @Override
    public boolean deleteReviewTemplateVersion(String projectId, String reviewId) throws IOException {
        if (projectId == null || projectId.trim().isEmpty() || 
            reviewId == null || reviewId.trim().isEmpty()) {
            return false;
        }
        
        try {
            ILocation workItemLocation = getWorkItemLocation(projectId, reviewId);
            if (workItemLocation == null) {
                return false;
            }
            
            ILocation versionFile = workItemLocation.append(REVIEW_VERSION_FILE);
            return repositoryFileService.deleteFile(versionFile);
        } catch (Exception e) {
            logger.error("Failed to delete review template version for review: {} from project: {}", reviewId, projectId, e);
            return false;
        }
    }
    
    @Override
    public long countSnapshots(String projectId, String templateId) throws IOException {
        // SVN存储模式暂不支持统计功能
        logger.warn("SVN存储模式不支持快照统计功能");
        return 0;
    }
    
    @Override
    public boolean existsSnapshot(String projectId, String snapshotId) throws IOException {
        if (projectId == null || projectId.trim().isEmpty() || 
            snapshotId == null || snapshotId.trim().isEmpty()) {
            return false;
        }
        
        try {
            ILocation snapshotFile = getSnapshotFileLocation(projectId, snapshotId);
            return repositoryFileService.exists(snapshotFile);
        } catch (Exception e) {
            logger.error("Failed to check snapshot existence: {} in project: {}", snapshotId, projectId, e);
            return false;
        }
    }
    
    @Override
    public boolean existsReviewTemplateVersion(String projectId, String reviewId) throws IOException {
        if (projectId == null || projectId.trim().isEmpty() || 
            reviewId == null || reviewId.trim().isEmpty()) {
            return false;
        }
        
        try {
            ILocation workItemLocation = getWorkItemLocation(projectId, reviewId);
            if (workItemLocation == null) {
                return false;
            }
            
            ILocation versionFile = workItemLocation.append(REVIEW_VERSION_FILE);
            return repositoryFileService.exists(versionFile);
        } catch (Exception e) {
            logger.error("Failed to check review template version existence for review: {} in project: {}", reviewId, projectId, e);
            return false;
        }
    }

    /**
     * 获取项目根路径
     * 参考 ChecklistTemplateSvnRepository 的实现
     *
     * @param projectId 项目ID
     * @return 项目根路径
     */
    private ILocation getProjectRootLocation(String projectId) {
        return projectLocationCache.computeIfAbsent(projectId, id -> {
            try {
                IProjectService projectService = PlatformContext.getPlatform().lookupService(IProjectService.class);
                return projectService.getProject(id).getLocation();
            } catch (Exception e) {
                logger.error("Failed to get project location for project: {}", id, e);
                throw new RuntimeException("无法获取项目路径: " + id, e);
            }
        });
    }

    /**
     * 构建快照文件路径
     *
     * @param projectId 项目ID
     * @param snapshotId 快照ID
     * @return 文件路径
     */
    private ILocation getSnapshotFileLocation(String projectId, String snapshotId) {
        return getSnapshotDirectoryLocation(projectId).append(snapshotId + FILE_EXTENSION);
    }

    /**
     * 构建快照目录路径
     *
     * @param projectId 项目ID
     * @return 目录路径
     */
    private ILocation getSnapshotDirectoryLocation(String projectId) {
        ILocation projectRoot = getProjectRootLocation(projectId);
        return projectRoot.append(SNAPSHOTS_DIRECTORY);
    }

    /**
     * 获取工作项位置
     */
    private ILocation getWorkItemLocation(String projectId, String workItemId) {
        try {
            ITrackerService trackerService = PlatformContext.getPlatform().lookupService(ITrackerService.class);
            IWorkItem workItem = trackerService.getWorkItem(projectId, workItemId);
            if (workItem == null || workItem.isUnresolvable()) {
                logger.warn("Cannot get location for non-existent work item: {}", workItemId);
                return null;
            }
            return workItem.getLocation().getParentLocation();
        } catch (Exception e) {
            logger.error("Failed to get work item location: {} in project: {}", workItemId, projectId, e);
            return null;
        }
    }
}
